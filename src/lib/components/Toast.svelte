<script lang="ts">
	import { fade } from 'svelte/transition';
	import { toasts, removeToast, clearAllToasts } from '$lib/stores/Toast';
	import { Info, X as XIcon, CheckCircle, AlertTriangle, AlertCircle } from 'lucide-svelte';
	import { createPortal } from '$lib/actions/createPortal';
	import { onDestroy } from 'svelte';

	// daisyUI 5 compliant theme styles using semantic colors
	const themeStyles = {
		success: {
			alertClass: 'alert-success',
			iconClass: 'text-success-content bg-success/20',
			component: CheckCircle
		},
		danger: {
			alertClass: 'alert-error',
			iconClass: 'text-error-content bg-error/20',
			component: AlertCircle
		},
		warning: {
			alertClass: 'alert-warning',
			iconClass: 'text-warning-content bg-warning/20',
			component: AlertTriangle
		},
		info: {
			alertClass: 'alert-info',
			iconClass: 'text-info-content bg-info/20',
			component: Info
		}
	};

	// Limpiar todos los toasts cuando se destruye el componente
	onDestroy(() => {
		clearAllToasts();
	});
</script>

<!-- Using daisyUI 5 toast component with proper positioning -->
<div use:createPortal class="toast toast-top toast-end">
	{#each $toasts as toast (toast.id)}
		<div
			transition:fade
			class="alert {themeStyles[toast.type].alertClass} shadow-lg max-w-xs"
			role="alert"
		>
			<div
				class="inline-flex items-center justify-center shrink-0 w-8 h-8 rounded-lg {themeStyles[toast.type].iconClass}"
			>
				<svelte:component this={themeStyles[toast.type].component} class="w-4 h-4" />
			</div>
			<div class="text-sm font-normal">
				{toast.title}
			</div>
			<button
				type="button"
				onclick={() => removeToast(toast.id)}
				class="btn btn-ghost btn-circle btn-xs"
				aria-label="Close"
			>
				<XIcon class="w-4 h-4" />
			</button>
		</div>
	{/each}
</div>
